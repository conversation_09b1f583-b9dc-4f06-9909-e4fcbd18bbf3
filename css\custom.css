/* Custom styles for nav buttons */
.nav-btn {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.55);
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-btn:hover {
  color: rgba(0, 0, 0, 0.7);
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-btn.active {
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(0, 0, 0, 0.1);
}

.nav-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Animation for button press */
.nav-btn:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Ripple effect animation */
.nav-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.nav-btn:hover::before {
  width: 100%;
  height: 100%;
}

/* Ensure dropdown items are also styled */
.dropdown-item {
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  transform: translateX(5px);
  background-color: #f8f9fa;
}

/* Keyframe for ripple animation */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}


.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('../img/IMG-20250819-WA0065.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.hero-section h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    font-weight: 700;
}

.hero-section p {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    font-size: 1.25rem;
}

.btn-primary {
    padding: 0.75rem 2rem;
    border-radius: 30px;
}

/* Language Toggle Animations */
.language-toggle-container {
  position: relative;
}

.language-toggle-container svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border-radius: 3px;
  padding: 2px;
}

.language-toggle-container svg:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Flag animation states */
.flag-en {
  opacity: 1;
  transform: translateX(0);
}

.flag-id {
  opacity: 0.6;
  transform: translateX(0);
}

/* When Indonesian is selected */
.language-toggle-container.id-active .flag-en {
  opacity: 0.6;
  transform: translateX(-2px);
}

.language-toggle-container.id-active .flag-id {
  opacity: 1;
  transform: translateX(2px);
  box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.3);
}

/* When English is selected */
.language-toggle-container.en-active .flag-en {
  opacity: 1;
  transform: translateX(-2px);
  box-shadow: 0 0 0 2px rgba(1, 33, 105, 0.3);
}

.language-toggle-container.en-active .flag-id {
  opacity: 0.6;
  transform: translateX(2px);
}

/* Enhanced switch animation */
.form-check-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.form-check-input:checked {
  background-color: #dc3545;
  border-color: #dc3545;
  transform: scale(1.05);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-input:not(:checked) {
  background-color: #012169;
  border-color: #012169;
  transform: scale(1.05);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-input:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Pulse animation for active flag */
@keyframes flagPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.flag-active {
  animation: flagPulse 0.6s ease-in-out;
}

/* Smooth slide animation for switch */
@keyframes switchSlide {
  0% { transform: translateX(-10px); opacity: 0.8; }
  100% { transform: translateX(0); opacity: 1; }
}

.form-check-input:checked {
  animation: switchSlide 0.3s ease-out;
}

/* Desktop navbar layout - restore proper positioning */
@media (min-width: 992px) {
  .navbar .container-fluid {
    display: flex;
    align-items: center;
  }

  /* Logo on the left */
  .navbar-brand-container {
    flex: 1;
    order: 1;
  }

  /* Navigation in the center */
  .navbar-collapse {
    flex: 1;
    order: 2;
  }

  /* Language toggle on the right */
  .navbar .container-fluid > .navbar-nav {
    flex: 1;
    order: 3;
    justify-content: flex-end;
  }

  /* Hide hamburger menu on desktop */
  .navbar-toggler {
    display: none;
  }
}

/* Mobile navbar layout adjustments */
@media (max-width: 991.98px) {
  /* Container for mobile navbar layout */
  .navbar .container-fluid {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
  }

  /* Center the logo on mobile */
  .navbar-brand-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    flex: none !important;
    z-index: 1;
  }

  /* Position hamburger menu on the left */
  .navbar-toggler {
    order: 1;
    margin-left: 0;
    margin-right: auto;
    z-index: 2;
  }

  /* Position language toggle on the right - now outside collapsible menu */
  .navbar .container-fluid > .navbar-nav {
    order: 3;
    flex: none !important;
    margin-left: auto;
    z-index: 2;
  }

  /* Ensure the language toggle container is properly positioned */
  .navbar .container-fluid > .navbar-nav .language-toggle-container {
    margin-left: auto;
  }

  /* Adjust collapsed menu to appear below the navbar */
  .navbar-collapse {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bs-navbar-bg, #fff);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    order: 4;
    width: 100%;
  }

  /* Reset the center navigation flex properties for mobile */
  .navbar-collapse .navbar-nav.mx-auto {
    flex: none !important;
    margin: 0 !important;
  }

  /* Ensure proper spacing for mobile layout */
  .navbar-brand-container {
    max-width: calc(100% - 120px); /* Account for hamburger + language toggle */
  }

  .navbar-brand img {
    max-width: 80px; /* Slightly smaller logo on mobile if needed */
  }
}

